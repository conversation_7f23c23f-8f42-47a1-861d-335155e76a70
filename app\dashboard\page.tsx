"use client"

import { useState, useEffect } from "react"
import { useSession } from "next-auth/react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { BookOpen, Trophy, Clock, TrendingUp, Play, Star, Loader2, Users, Award } from "lucide-react"
import { useAuth } from "@/lib/store"
import { toast } from "sonner"
import Link from "next/link"

interface DashboardData {
  stats: {
    totalQuizzes: number
    totalPoints: number
    averageScore: number
    currentStreak: number
    totalAchievements: number
  }
  recentAttempts: Array<{
    id: string
    quiz: {
      title: string
      type: string
      difficulty: string
    }
    score: number
    percentage: number
    completedAt: string
  }>
  enrolledQuizzes: Array<{
    id: string
    title: string
    description: string
    type: string
    difficulty: string
    thumbnail: string | null
    timeLimit: number | null
    startTime: string | null
    endTime: string | null
    enrolledAt: string
  }>
  achievements: Array<{
    id: string
    type: string
    title: string
    description: string
    unlockedAt: string
  }>
  upcomingQuizzes: Array<{
    id: string
    title: string
    type: string
    startTime: string | null
    endTime: string | null
    timeLimit: number | null
  }>
  leaderboardPosition: {
    position: number
    totalPoints: number
  } | null
}

export default function StudentDashboard() {
  const { data: session, status } = useSession()
  const { isLoading: authLoading } = useAuth()
  const [dashboardData, setDashboardData] = useState<DashboardData | null>(null)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    if (session?.user?.role === 'STUDENT') {
      fetchDashboardData()
    }
  }, [session])

  const fetchDashboardData = async () => {
    try {
      setLoading(true)
      const response = await fetch('/api/student/dashboard')

      if (!response.ok) {
        throw new Error('Failed to fetch dashboard data')
      }

      const result = await response.json()
      setDashboardData(result.data)
    } catch (error) {
      console.error('Error fetching dashboard data:', error)
      toast.error('Failed to load dashboard data')
    } finally {
      setLoading(false)
    }
  }

  if (status === "loading" || authLoading || loading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="animate-pulse">
          <div className="h-8 bg-muted rounded w-1/4 mb-4"></div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            {[...Array(4)].map((_, i) => (
              <div key={i} className="h-32 bg-muted rounded"></div>
            ))}
          </div>
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            {[...Array(2)].map((_, i) => (
              <div key={i} className="h-64 bg-muted rounded"></div>
            ))}
          </div>
        </div>
      </div>
    )
  }

  if (!session || session.user.role !== 'STUDENT') {
    return (
      <div className="container mx-auto px-4 py-8">
        <Card className="max-w-md mx-auto">
          <CardHeader>
            <CardTitle className="text-destructive">Access Denied</CardTitle>
            <CardDescription>
              You need student privileges to access this page.
            </CardDescription>
          </CardHeader>
        </Card>
      </div>
    )
  }

  if (!dashboardData) {
    return (
      <div className="container mx-auto px-4 py-8">
        <Card className="max-w-md mx-auto">
          <CardHeader>
            <CardTitle>No Data Available</CardTitle>
            <CardDescription>
              Unable to load dashboard data. Please try refreshing the page.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Button onClick={fetchDashboardData} className="w-full">
              <Loader2 className="h-4 w-4 mr-2 animate-spin" />
              Retry
            </Button>
          </CardContent>
        </Card>
      </div>
    )
  }

  const stats = [
    {
      title: "Quizzes Completed",
      value: dashboardData.stats.totalQuizzes.toString(),
      icon: BookOpen,
      change: `${dashboardData.recentAttempts.length} recent attempts`
    },
    {
      title: "Average Score",
      value: `${dashboardData.stats.averageScore}%`,
      icon: TrendingUp,
      change: "Keep improving!"
    },
    {
      title: "Current Streak",
      value: `${dashboardData.stats.currentStreak} days`,
      icon: Clock,
      change: "Stay consistent!"
    },
    {
      title: "Achievements",
      value: dashboardData.stats.totalAchievements.toString(),
      icon: Trophy,
      change: `${dashboardData.achievements.length} unlocked`
    },
  ]

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="flex items-center justify-between mb-8">
        <div>
          <h1 className="text-3xl font-bold flex items-center gap-2">
            <BookOpen className="h-8 w-8" />
            Student Dashboard
          </h1>
          <p className="text-muted-foreground mt-2">
            Welcome back, {session.user.name}! Ready to learn?
          </p>
        </div>
        <Badge variant="default" className="flex items-center gap-1">
          <Star className="h-3 w-3" />
          STUDENT
        </Badge>
      </div>

      {/* Stats Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        {stats.map((stat, index) => (
          <Card key={index}>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                {stat.title}
              </CardTitle>
              <stat.icon className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stat.value}</div>
              <p className="text-xs text-muted-foreground">
                {stat.change}
              </p>
            </CardContent>
          </Card>
        ))}
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* Recent Quiz Results */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Trophy className="h-5 w-5" />
              Recent Quiz Results
            </CardTitle>
            <CardDescription>
              Your latest quiz performances
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {dashboardData.recentAttempts.length > 0 ? (
                dashboardData.recentAttempts.map((attempt) => (
                  <div key={attempt.id} className="flex items-center justify-between p-3 border rounded-lg">
                    <div>
                      <h4 className="font-semibold">{attempt.quiz.title}</h4>
                      <p className="text-sm text-muted-foreground">
                        {new Date(attempt.completedAt).toLocaleDateString()} • {attempt.quiz.type}
                      </p>
                    </div>
                    <div className="text-right">
                      <div className="text-lg font-bold text-green-600">{attempt.percentage}%</div>
                      <Badge variant="outline" className="text-xs">
                        {attempt.quiz.difficulty}
                      </Badge>
                    </div>
                  </div>
                ))
              ) : (
                <div className="text-center py-8 text-muted-foreground">
                  <Trophy className="h-12 w-12 mx-auto mb-4 opacity-50" />
                  <p>No quiz attempts yet</p>
                  <p className="text-sm">Start your first quiz to see results here</p>
                </div>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Enrolled Quizzes */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Play className="h-5 w-5" />
              Enrolled Quizzes
            </CardTitle>
            <CardDescription>
              Continue with your enrolled quizzes
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {dashboardData.enrolledQuizzes.length > 0 ? (
                dashboardData.enrolledQuizzes.slice(0, 3).map((quiz) => (
                  <div key={quiz.id} className="flex items-center justify-between p-3 border rounded-lg">
                    <div>
                      <h4 className="font-semibold">{quiz.title}</h4>
                      <p className="text-sm text-muted-foreground">
                        {quiz.type} • {quiz.timeLimit ? `${quiz.timeLimit} min` : 'No time limit'}
                      </p>
                    </div>
                    <div className="flex items-center gap-2">
                      <Badge variant="outline" className="text-xs">
                        {quiz.difficulty}
                      </Badge>
                      <Link href={`/quiz/${quiz.id}`}>
                        <Button size="sm">
                          Start Quiz
                        </Button>
                      </Link>
                    </div>
                  </div>
                ))
              ) : (
                <div className="text-center py-8 text-muted-foreground">
                  <Play className="h-12 w-12 mx-auto mb-4 opacity-50" />
                  <p>No enrolled quizzes</p>
                  <p className="text-sm">Browse available quizzes to get started</p>
                  <Link href="/quizzes">
                    <Button className="mt-4" size="sm">
                      Browse Quizzes
                    </Button>
                  </Link>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Quick Actions */}
      <div className="mt-8">
        <Card>
          <CardHeader>
            <CardTitle>Quick Actions</CardTitle>
            <CardDescription>
              Common tasks and shortcuts
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              <Link href="/quizzes">
                <Button variant="outline" className="h-16 flex flex-col gap-2 w-full">
                  <BookOpen className="h-5 w-5" />
                  Browse All Quizzes
                </Button>
              </Link>
              <Link href="/achievements">
                <Button variant="outline" className="h-16 flex flex-col gap-2 w-full">
                  <Trophy className="h-5 w-5" />
                  View Achievements
                </Button>
              </Link>
              <Link href="/analytics">
                <Button variant="outline" className="h-16 flex flex-col gap-2 w-full">
                  <TrendingUp className="h-5 w-5" />
                  Progress Report
                </Button>
              </Link>
              <Link href="/leaderboard">
                <Button variant="outline" className="h-16 flex flex-col gap-2 w-full">
                  <Users className="h-5 w-5" />
                  Leaderboard
                  {dashboardData.leaderboardPosition && (
                    <span className="text-xs">#{dashboardData.leaderboardPosition.position}</span>
                  )}
                </Button>
              </Link>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Recent Achievements */}
      {dashboardData.achievements.length > 0 && (
        <div className="mt-8">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Award className="h-5 w-5" />
                Recent Achievements
              </CardTitle>
              <CardDescription>
                Your latest unlocked achievements
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {dashboardData.achievements.slice(0, 3).map((achievement) => (
                  <div key={achievement.id} className="flex items-center gap-3 p-3 border rounded-lg">
                    <div className="text-2xl">🏆</div>
                    <div>
                      <h4 className="font-semibold">{achievement.title}</h4>
                      <p className="text-sm text-muted-foreground">{achievement.description}</p>
                      <p className="text-xs text-muted-foreground">
                        {new Date(achievement.unlockedAt).toLocaleDateString()}
                      </p>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>
      )}
    </div>
  )
}
