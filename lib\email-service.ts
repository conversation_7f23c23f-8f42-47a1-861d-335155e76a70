import nodemailer from 'nodemailer'
import { prisma } from '@/lib/prisma'

interface EmailConfig {
  smtpHost: string
  smtpPort: number
  smtpUser: string
  smtpPassword: string
  smtpSecure: boolean
  fromEmail: string
  fromName: string
  enableEmailNotifications: boolean
}

interface EmailOptions {
  to: string | string[]
  subject: string
  html?: string
  text?: string
  attachments?: Array<{
    filename: string
    content: Buffer | string
    contentType?: string
  }>
}

class EmailService {
  private transporter: nodemailer.Transporter | null = null
  private config: EmailConfig | null = null

  /**
   * Initialize email service with configuration
   */
  async initialize(): Promise<void> {
    try {
      // Get email settings from database
      
      const settings = await prisma.systemSetting.findMany({
        where: {
          category: 'email'
        }
      })

      if (settings.length === 0) {
        console.warn('No email settings found in database')
        return
      }

      // Convert settings array to config object
      const settingsMap = settings.reduce((acc, setting) => {
        acc[setting.key] = setting.value
        return acc
      }, {} as Record<string, any>)

      this.config = {
        smtpHost: settingsMap.smtpHost || '',
        smtpPort: parseInt(settingsMap.smtpPort) || 587,
        smtpUser: settingsMap.smtpUser || '',
        smtpPassword: settingsMap.smtpPassword || '',
        smtpSecure: settingsMap.smtpSecure === 'true',
        fromEmail: settingsMap.fromEmail || '',
        fromName: settingsMap.fromName || 'QuizMaster',
        enableEmailNotifications: settingsMap.enableEmailNotifications !== 'false'
      }

      // Only create transporter if email notifications are enabled and configured
      if (this.config.enableEmailNotifications && this.isConfigured()) {
        await this.createTransporter()
      }
    } catch (error) {
      console.error('Failed to initialize email service:', error)
    }
  }

  /**
   * Check if email service is properly configured
   */
  private isConfigured(): boolean {
    return !!(
      this.config?.smtpHost &&
      this.config?.smtpUser &&
      this.config?.smtpPassword &&
      this.config?.fromEmail
    )
  }

  /**
   * Create nodemailer transporter
   */
  private async createTransporter(): Promise<void> {
    if (!this.config) {
      throw new Error('Email configuration not loaded')
    }

    this.transporter = nodemailer.createTransporter({
      host: this.config.smtpHost,
      port: this.config.smtpPort,
      secure: this.config.smtpSecure,
      auth: {
        user: this.config.smtpUser,
        pass: this.config.smtpPassword
      },
      tls: {
        rejectUnauthorized: false // Allow self-signed certificates
      }
    })

    // Verify connection
    try {
      await this.transporter.verify()
      console.log('Email service connected successfully')
    } catch (error) {
      console.error('Email service connection failed:', error)
      this.transporter = null
      throw error
    }
  }

  /**
   * Send email
   */
  async sendEmail(options: EmailOptions): Promise<boolean> {
    try {
      // Check if email service is available
      if (!this.transporter || !this.config) {
        console.warn('Email service not configured or disabled')
        return false
      }

      if (!this.config.enableEmailNotifications) {
        console.log('Email notifications are disabled')
        return false
      }

      // Prepare email options
      const mailOptions = {
        from: `"${this.config.fromName}" <${this.config.fromEmail}>`,
        to: Array.isArray(options.to) ? options.to.join(', ') : options.to,
        subject: options.subject,
        html: options.html,
        text: options.text,
        attachments: options.attachments
      }

      // Send email
      const result = await this.transporter.sendMail(mailOptions)
      console.log('Email sent successfully:', result.messageId)
      return true

    } catch (error) {
      console.error('Failed to send email:', error)
      return false
    }
  }

  /**
   * Test email configuration
   */
  async testConnection(testConfig?: EmailConfig): Promise<{ success: boolean; message: string }> {
    try {
      const configToTest = testConfig || this.config

      if (!configToTest) {
        return { success: false, message: 'No email configuration available' }
      }

      // Create test transporter
      const testTransporter = nodemailer.createTransporter({
        host: configToTest.smtpHost,
        port: configToTest.smtpPort,
        secure: configToTest.smtpSecure,
        auth: {
          user: configToTest.smtpUser,
          pass: configToTest.smtpPassword
        },
        tls: {
          rejectUnauthorized: false
        }
      })

      // Verify connection
      await testTransporter.verify()
      
      return { 
        success: true, 
        message: 'Email configuration is valid and connection successful' 
      }

    } catch (error: any) {
      return { 
        success: false, 
        message: `Email connection failed: ${error.message}` 
      }
    }
  }

  /**
   * Send test email
   */
  async sendTestEmail(to: string, testConfig?: EmailConfig): Promise<{ success: boolean; message: string }> {
    try {
      const configToTest = testConfig || this.config

      if (!configToTest) {
        return { success: false, message: 'No email configuration available' }
      }

      // Create test transporter
      const testTransporter = nodemailer.createTransporter({
        host: configToTest.smtpHost,
        port: configToTest.smtpPort,
        secure: configToTest.smtpSecure,
        auth: {
          user: configToTest.smtpUser,
          pass: configToTest.smtpPassword
        },
        tls: {
          rejectUnauthorized: false
        }
      })

      // Send test email
      const result = await testTransporter.sendMail({
        from: `"${configToTest.fromName}" <${configToTest.fromEmail}>`,
        to,
        subject: 'QuizMaster Email Test',
        html: `
          <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
            <h2 style="color: #333;">Email Configuration Test</h2>
            <p>This is a test email from QuizMaster to verify your email configuration.</p>
            <p><strong>Test Details:</strong></p>
            <ul>
              <li>SMTP Host: ${configToTest.smtpHost}</li>
              <li>SMTP Port: ${configToTest.smtpPort}</li>
              <li>Secure: ${configToTest.smtpSecure ? 'Yes' : 'No'}</li>
              <li>From: ${configToTest.fromEmail}</li>
            </ul>
            <p>If you received this email, your configuration is working correctly!</p>
            <hr style="margin: 20px 0; border: none; border-top: 1px solid #eee;">
            <p style="color: #666; font-size: 12px;">
              This is an automated test email from QuizMaster.
            </p>
          </div>
        `,
        text: `
          Email Configuration Test
          
          This is a test email from QuizMaster to verify your email configuration.
          
          Test Details:
          - SMTP Host: ${configToTest.smtpHost}
          - SMTP Port: ${configToTest.smtpPort}
          - Secure: ${configToTest.smtpSecure ? 'Yes' : 'No'}
          - From: ${configToTest.fromEmail}
          
          If you received this email, your configuration is working correctly!
        `
      })

      return { 
        success: true, 
        message: `Test email sent successfully to ${to}. Message ID: ${result.messageId}` 
      }

    } catch (error: any) {
      return { 
        success: false, 
        message: `Failed to send test email: ${error.message}` 
      }
    }
  }

  /**
   * Get current configuration status
   */
  getStatus(): { configured: boolean; enabled: boolean; connected: boolean } {
    return {
      configured: this.isConfigured(),
      enabled: this.config?.enableEmailNotifications || false,
      connected: !!this.transporter
    }
  }
}

// Create singleton instance
export const emailService = new EmailService()

// Initialize on module load
emailService.initialize().catch(console.error)
